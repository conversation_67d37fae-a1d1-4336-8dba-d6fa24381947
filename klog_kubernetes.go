package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// loadKubeContexts returns a command that loads available Kubernetes contexts
func (m *KlogModel) loadKubeContexts() tea.Cmd {
	return func() tea.Msg {
		loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
		configOverrides := &clientcmd.ConfigOverrides{}
		
		kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)
		rawConfig, err := kubeConfig.RawConfig()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to load kubeconfig: %v", err))
		}
		
		var contexts []string
		for name := range rawConfig.Contexts {
			contexts = append(contexts, name)
		}
		sort.Strings(contexts)
		
		return kubeContextsMsg{contexts: contexts}
	}
}

// loadNamespaces returns a command that loads namespaces for the selected context
func (m *KlogModel) loadNamespaces() tea.Cmd {
	return func() tea.Msg {
		clientset, err := m.createKubernetesClient()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to create Kubernetes client: %v", err))
		}
		
		namespaceList, err := clientset.CoreV1().Namespaces().List(context.Background(), metav1.ListOptions{})
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to list namespaces: %v", err))
		}
		
		var namespaces []string
		for _, ns := range namespaceList.Items {
			namespaces = append(namespaces, ns.Name)
		}
		sort.Strings(namespaces)
		
		return kubeNamespacesMsg{namespaces: namespaces}
	}
}

// loadDeployments returns a command that loads deployments for the selected namespace
func (m *KlogModel) loadDeployments() tea.Cmd {
	return func() tea.Msg {
		clientset, err := m.createKubernetesClient()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to create Kubernetes client: %v", err))
		}
		
		deploymentList, err := clientset.AppsV1().Deployments(m.namespace).List(context.Background(), metav1.ListOptions{})
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to list deployments: %v", err))
		}
		
		var deployments []string
		for _, deployment := range deploymentList.Items {
			deployments = append(deployments, deployment.Name)
		}
		sort.Strings(deployments)
		
		return kubeDeploymentsMsg{deployments: deployments}
	}
}

// createKubernetesClient creates a Kubernetes clientset for the selected context
func (m *KlogModel) createKubernetesClient() (*kubernetes.Clientset, error) {
	loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
	configOverrides := &clientcmd.ConfigOverrides{
		CurrentContext: m.context,
	}
	
	kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)
	config, err := kubeConfig.ClientConfig()
	if err != nil {
		return nil, err
	}
	
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, err
	}
	
	m.clientset = clientset
	return clientset, nil
}

// initializeSession sets up the session directory and log file
func (m *KlogModel) initializeSession() tea.Cmd {
	return func() tea.Msg {
		// Create session directory
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to get home directory: %v", err))
		}
		
		sessionDir := filepath.Join(homeDir, "klog", "sessions")
		if err := os.MkdirAll(sessionDir, 0755); err != nil {
			return errorMsg(fmt.Sprintf("Failed to create session directory: %v", err))
		}
		
		// Create log file
		logFileName := fmt.Sprintf("%s_%s_%s.log", m.context, m.namespace, m.deployment)
		logFilePath := filepath.Join(sessionDir, logFileName)
		
		logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to create log file: %v", err))
		}
		
		m.sessionDir = sessionDir
		m.logFile = logFile
		
		return nil
	}
}

// startStreaming begins streaming logs from all pods in the deployment
func (m *KlogModel) startStreaming() tea.Cmd {
	return func() tea.Msg {
		if m.clientset == nil {
			return errorMsg("Kubernetes client not initialized")
		}
		
		// Find pods for the deployment
		pods, err := m.findPodsForDeployment()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to find pods: %v", err))
		}
		
		// Initialize pod streams
		m.mu.Lock()
		for i, pod := range pods {
			if len(m.activePods) >= m.maxPods {
				break
			}
			
			colorIndex := i % len(podColors)
			podStream := &PodLogStream{
				PodName:   pod.Name,
				Buffer:    make([]LogEntry, 0, 1000),
				MaxBuffer: 1000,
				Active:    true,
				Color:     podColors[colorIndex],
			}
			
			m.podStreams[pod.Name] = podStream
			m.activePods = append(m.activePods, pod.Name)
		}
		m.mu.Unlock()
		
		// Start streaming from each pod
		var cmds []tea.Cmd
		for _, podName := range m.activePods {
			cmds = append(cmds, m.streamPodLogs(podName))
		}
		
		return tea.Batch(cmds...)
	}
}

// findPodsForDeployment finds all pods belonging to the selected deployment
func (m *KlogModel) findPodsForDeployment() ([]corev1.Pod, error) {
	// Get the deployment to find its selector
	deployment, err := m.clientset.AppsV1().Deployments(m.namespace).Get(
		context.Background(), m.deployment, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment: %v", err)
	}
	
	// Convert selector to label selector string
	selector := metav1.FormatLabelSelector(deployment.Spec.Selector)
	
	// Find pods with matching labels
	podList, err := m.clientset.CoreV1().Pods(m.namespace).List(
		context.Background(), metav1.ListOptions{LabelSelector: selector})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %v", err)
	}
	
	// Filter for running pods
	var runningPods []corev1.Pod
	for _, pod := range podList.Items {
		if pod.Status.Phase == corev1.PodRunning {
			runningPods = append(runningPods, pod)
		}
	}
	
	// Sort by creation time (newest first)
	sort.Slice(runningPods, func(i, j int) bool {
		return runningPods[i].CreationTimestamp.After(runningPods[j].CreationTimestamp.Time)
	})
	
	return runningPods, nil
}

// streamPodLogs returns a command that streams logs from a specific pod
func (m *KlogModel) streamPodLogs(podName string) tea.Cmd {
	return func() tea.Msg {
		req := m.clientset.CoreV1().Pods(m.namespace).GetLogs(podName, &corev1.PodLogOptions{
			Follow:    true,
			TailLines: &[]int64{100}[0], // Start with last 100 lines
		})
		
		stream, err := req.Stream(context.Background())
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to stream logs from pod %s: %v", podName, err))
		}
		
		// Start goroutine to read logs
		go m.readPodLogs(podName, stream)
		
		return nil
	}
}

// readPodLogs reads logs from a pod stream and sends them as messages
func (m *KlogModel) readPodLogs(podName string, reader io.ReadCloser) {
	defer reader.Close()
	
	scanner := bufio.NewScanner(reader)
	podStream := m.podStreams[podName]
	
	for scanner.Scan() {
		if !podStream.Active {
			continue
		}
		
		line := scanner.Text()
		timestamp := time.Now()
		
		entry := LogEntry{
			Timestamp: timestamp,
			PodName:   podName,
			Message:   line,
			Original:  line,
		}
		
		// Add to pod buffer
		podStream.mu.Lock()
		podStream.Buffer = append(podStream.Buffer, entry)
		if len(podStream.Buffer) > podStream.MaxBuffer {
			// Remove oldest entries
			copy(podStream.Buffer, podStream.Buffer[1:])
			podStream.Buffer = podStream.Buffer[:podStream.MaxBuffer]
		}
		podStream.mu.Unlock()
		
		// Add to main log buffer (only if not paused)
		if !m.paused {
			m.mu.Lock()
			m.logBuffer = append(m.logBuffer, entry)
			if len(m.logBuffer) > m.config.BufferSize {
				// Remove oldest entries
				copy(m.logBuffer, m.logBuffer[1:])
				m.logBuffer = m.logBuffer[:m.config.BufferSize]
			}
			m.mu.Unlock()
		}
		
		// Write to disk
		if m.logFile != nil {
			logLine := fmt.Sprintf("[%s] [%s] %s\n", 
				timestamp.Format("2006-01-02 15:04:05.000"), podName, line)
			m.logFile.WriteString(logLine)
			m.logFile.Sync()
		}
	}
	
	if err := scanner.Err(); err != nil {
		// Log error but don't crash
		fmt.Printf("Error reading logs from pod %s: %v\n", podName, err)
	}
}

// refreshPods discovers new pods and starts streaming from them
func (m *KlogModel) refreshPods() tea.Cmd {
	return func() tea.Msg {
		pods, err := m.findPodsForDeployment()
		if err != nil {
			return errorMsg(fmt.Sprintf("Failed to refresh pods: %v", err))
		}
		
		m.mu.Lock()
		defer m.mu.Unlock()
		
		// Find new pods
		existingPods := make(map[string]bool)
		for _, podName := range m.activePods {
			existingPods[podName] = true
		}
		
		var newCmds []tea.Cmd
		for i, pod := range pods {
			if !existingPods[pod.Name] && len(m.activePods) < m.maxPods {
				colorIndex := len(m.activePods) % len(podColors)
				podStream := &PodLogStream{
					PodName:   pod.Name,
					Buffer:    make([]LogEntry, 0, 1000),
					MaxBuffer: 1000,
					Active:    true,
					Color:     podColors[colorIndex],
				}
				
				m.podStreams[pod.Name] = podStream
				m.activePods = append(m.activePods, pod.Name)
				newCmds = append(newCmds, m.streamPodLogs(pod.Name))
			}
			
			if i >= m.maxPods {
				break
			}
		}
		
		if len(newCmds) > 0 {
			return tea.Batch(newCmds...)
		}
		
		return nil
	}
}

// stopStreaming stops all log streaming
func (m *KlogModel) stopStreaming() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	for _, podStream := range m.podStreams {
		podStream.Active = false
	}
	
	if m.logFile != nil {
		m.logFile.Close()
		m.logFile = nil
	}
} 