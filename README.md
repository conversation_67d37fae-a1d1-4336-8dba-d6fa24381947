# Development Tools Suite

A multi-tool CLI/TUI application framework built with Go, Cobra, and Bubble Tea. This framework provides a structured way to build and organize multiple development tools in a single application.

## Features

- **Interactive Tool Selection**: Beautiful TUI interface for selecting tools
- **Modular Architecture**: Easy to add new tools
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Professional UI**: Built with Bubble Tea and Lip Gloss for rich terminal interfaces

## Current Demo Tools

1. **Message Printer** - Displays a welcome message with styled borders
2. **Greeter** - Interactive name input with personalized greeting
3. **Counter** - Simple counter with increment/decrement controls

## Usage

### Running the Application

```bash
# Build the application
go build -o dev-tools.exe

# Run the tool selector
./dev-tools.exe

# Show help
./dev-tools.exe --help
```

### Navigation

- **Tool Selection**: Use ↑/↓ or k/j to navigate, Enter to select
- **General**: Press q, Esc, or Ctrl+C to quit/go back
- **Tool-specific controls** are shown in each tool's interface

## Architecture

### File Structure

- `main.go` - CLI entry point with Cobra setup
- `selector.go` - Tool selection TUI interface
- `tools.go` - Individual tool implementations
- `go.mod` - Dependencies

### Adding New Tools

To add a new tool:

1. **Create the Tool Model**: Implement the Bubble Tea model interface in `tools.go`
   ```go
   type myToolModel struct {
       // your state
   }
   
   func (m myToolModel) Init() tea.Cmd { /* ... */ }
   func (m myToolModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) { /* ... */ }
   func (m myToolModel) View() string { /* ... */ }
   ```

2. **Create Constructor Function**:
   ```go
   func newMyTool() (tea.Model, tea.Cmd) {
       return myToolModel{}, nil
   }
   ```

3. **Register in Tool List**: Add to the `tools` slice in `selector.go`
   ```go
   {
       Name:        "My Tool",
       Description: "What this tool does",
       Handler:     newMyTool,
   },
   ```

### Framework Benefits

- **Consistent Navigation**: All tools use standard quit/back patterns
- **Styled Interface**: Lip Gloss provides consistent styling
- **State Management**: Bubble Tea handles complex UI state
- **Keyboard Controls**: Standardized navigation patterns
- **Easy Testing**: Each tool is isolated and testable

## Dependencies

- **Cobra** - CLI framework for commands and flags
- **Bubble Tea** - TUI framework for interactive interfaces
- **Lip Gloss** - Styling and layout for terminal UIs
- **Bubbles** - Pre-built components (text input, tables, etc.)

## Building for Production

```bash
# Build for current platform
go build -o dev-tools

# Cross-compile for different platforms
GOOS=windows GOARCH=amd64 go build -o dev-tools.exe
GOOS=darwin GOARCH=amd64 go build -o dev-tools-mac
GOOS=linux GOARCH=amd64 go build -o dev-tools-linux
```

## Next Steps

This framework is ready for real tools! The demo tools show patterns for:
- Simple displays (Message Printer)
- Interactive input (Greeter)
- Stateful interfaces (Counter)

You can now build sophisticated tools like:
- Kubernetes log viewers
- Git workflow managers
- API testing tools
- Development environment helpers
- And much more! 