package main

import (
	"fmt"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

type Tool struct {
	Name        string
	Description string
	Handler     func() (tea.Model, tea.Cmd)
}

var tools = []Tool{
	{
		Name:        "Kubernetes Log Aggregator",
		Description: "Stream and aggregate logs from Kubernetes pods with search and clipboard support",
		Handler:     newKlog,
	},
	{
		Name:        "Message Printer",
		Description: "Displays a simple welcome message",
		Handler:     newMessagePrinter,
	},
	{
		Name:        "Greeter",
		Description: "Interactive name input with greeting",
		Handler:     newGreeter,
	},
	{
		Name:        "Counter",
		Description: "Simple counter with increment/decrement",
		Handler:     newCounter,
	},
}

type selectorModel struct {
	cursor   int
	selected bool
}

func (m selectorModel) Init() tea.Cmd {
	return nil
}

func (m selectorModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			return m, tea.Quit
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			if m.cursor < len(tools)-1 {
				m.cursor++
			}
		case "enter":
			// Start the selected tool
			return tools[m.cursor].Handler()
		}
	}
	return m, nil
}

func (m selectorModel) View() string {
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("205")).
		MarginBottom(1)

	itemStyle := lipgloss.NewStyle().
		PaddingLeft(2)

	selectedStyle := lipgloss.NewStyle().
		PaddingLeft(2).
		Foreground(lipgloss.Color("170")).
		Bold(true).
		Background(lipgloss.Color("240"))

	s := titleStyle.Render("🛠️  Development Tools") + "\n\n"
	s += "Select a tool to run:\n\n"

	for i, tool := range tools {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
		}

		line := fmt.Sprintf("%s %s - %s", cursor, tool.Name, tool.Description)
		if m.cursor == i {
			s += selectedStyle.Render(line) + "\n"
		} else {
			s += itemStyle.Render(line) + "\n"
		}
	}

	s += "\n" + lipgloss.NewStyle().Faint(true).Render("Press Enter to select, q to quit")
	return s
}

func startToolSelector() error {
	p := tea.NewProgram(selectorModel{})
	_, err := p.Run()
	return err
}
