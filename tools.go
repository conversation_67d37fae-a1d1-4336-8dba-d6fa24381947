package main

import (
	"fmt"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Tool 1: Message Printer
type messagePrinterModel struct{}

func newMessagePrinter() (tea.Model, tea.Cmd) {
	return messagePrinterModel{}, nil
}

func (m messagePrinterModel) Init() tea.Cmd {
	return nil
}

func (m messagePrinterModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc", "enter":
			return selectorModel{}, nil
		}
	}
	return m, nil
}

func (m messagePrinterModel) View() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		MarginBottom(1)

	messageStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("86")).
		Padding(1, 2).
		MarginTop(1).
		MarginBottom(1)

	s := headerStyle.Render("📄 Message Printer Tool") + "\n\n"
	s += messageStyle.Render("🎉 Welcome to the Development Tools Suite!\n\nThis is a demo tool that displays messages.\nYou can build amazing CLI tools with this framework!") + "\n"
	s += lipgloss.NewStyle().Faint(true).Render("Press any key to return to tool selection")

	return s
}

// Tool 2: Greeter
type greeterModel struct {
	textInput textinput.Model
	submitted bool
	name      string
}

func newGreeter() (tea.Model, tea.Cmd) {
	ti := textinput.New()
	ti.Placeholder = "Enter your name..."
	ti.Focus()
	ti.CharLimit = 50
	ti.Width = 30

	return greeterModel{
		textInput: ti,
		submitted: false,
	}, textinput.Blink
}

func (m greeterModel) Init() tea.Cmd {
	return textinput.Blink
}

func (m greeterModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "esc":
			return selectorModel{}, nil
		case "q":
			if m.submitted {
				return selectorModel{}, nil
			}
		case "enter":
			if !m.submitted {
				m.name = m.textInput.Value()
				m.submitted = true
				return m, nil
			} else {
				return selectorModel{}, nil
			}
		}
	}

	if !m.submitted {
		m.textInput, cmd = m.textInput.Update(msg)
	}
	return m, cmd
}

func (m greeterModel) View() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("212")).
		MarginBottom(1)

	greetingStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("212")).
		Padding(1, 2).
		MarginTop(1).
		MarginBottom(1)

	s := headerStyle.Render("👋 Greeter Tool") + "\n\n"

	if !m.submitted {
		s += "What's your name?\n\n"
		s += m.textInput.View() + "\n\n"
		s += lipgloss.NewStyle().Faint(true).Render("Press Enter to submit, Esc to go back")
	} else {
		greeting := fmt.Sprintf("Hello, %s! 🎉\n\nNice to meet you!", m.name)
		s += greetingStyle.Render(greeting) + "\n"
		s += lipgloss.NewStyle().Faint(true).Render("Press Enter or q to return to tool selection")
	}

	return s
}

// Tool 3: Counter
type counterModel struct {
	count int
}

func newCounter() (tea.Model, tea.Cmd) {
	return counterModel{count: 0}, nil
}

func (m counterModel) Init() tea.Cmd {
	return nil
}

func (m counterModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			return selectorModel{}, nil
		case "up", "k", "+":
			m.count++
		case "down", "j", "-":
			m.count--
		case "r":
			m.count = 0
		}
	}
	return m, nil
}

func (m counterModel) View() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("226")).
		MarginBottom(1)

	counterStyle := lipgloss.NewStyle().
		Border(lipgloss.DoubleBorder()).
		BorderForeground(lipgloss.Color("226")).
		Padding(2, 4).
		MarginTop(1).
		MarginBottom(1).
		Align(lipgloss.Center)

	countDisplay := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("226"))

	s := headerStyle.Render("🔢 Counter Tool") + "\n\n"
	s += counterStyle.Render(countDisplay.Render(fmt.Sprintf("%d", m.count))) + "\n"

	helpStyle := lipgloss.NewStyle().Faint(true)
	s += helpStyle.Render("Controls:\n")
	s += helpStyle.Render("  ↑/k/+  Increment\n")
	s += helpStyle.Render("  ↓/j/-  Decrement\n")
	s += helpStyle.Render("  r      Reset\n")
	s += helpStyle.Render("  q/esc  Back to menu")

	return s
}
